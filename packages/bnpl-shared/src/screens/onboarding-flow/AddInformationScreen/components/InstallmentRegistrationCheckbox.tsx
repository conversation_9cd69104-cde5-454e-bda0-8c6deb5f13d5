import { useABTesting } from 'bnpl-shared/src/hooks/useABTesting';
import { CheckBox } from 'bnpl-shared/src/shared/CheckBox/CheckBox';
import { AppText } from 'bnpl-shared/src/shared/react-native-customized';
import { ABTestingGroup, ExperimentName } from 'bnpl-shared/src/types';
import React, { useEffect, useState } from 'react';
import { ViewStyle } from 'react-native';

const whiteListKey = ExperimentName.INSTALLMENT_REGISTRATION_CHECKBOX;

export const InstallmentRegistrationCheckbox = ({
  onChangeValue,
  style,
}: {
  onChangeValue: (checked: boolean) => void;
  style?: ViewStyle;
}) => {
  const { fetchABTestResult, isInWhiteList } = useABTesting();
  const [checkedInstallmentRegistration, setCheckedInstallmentRegistration] = useState(false);
  const shouldShowInstallmentRegistrationCheckBox = isInWhiteList(whiteListKey);
  useEffect(() => {
    onChangeValue?.(checkedInstallmentRegistration);
  }, [checkedInstallmentRegistration, onChangeValue]);

  useEffect(() => {
    (async () => {
      const abTestingResult = await fetchABTestResult(whiteListKey);
      const shouldAutoCheckInstallmentRegistrationCheckbox =
        shouldShowInstallmentRegistrationCheckBox &&
        abTestingResult?.toLowerCase() === ABTestingGroup.Variation_1.toLowerCase();
      setCheckedInstallmentRegistration(!!shouldAutoCheckInstallmentRegistrationCheckbox);
    })();
  }, []);

  return (
    <>
      {shouldShowInstallmentRegistrationCheckBox ? (
        <CheckBox
          testID="installment-registration-checkbox"
          style={style}
          value={checkedInstallmentRegistration}
          onChangeValue={checked => {
            setCheckedInstallmentRegistration(checked);
          }}
          title={<AppText>Tôi đồng ý đăng ký sử dụng thêm dịch vụ Trả góp</AppText>}
        />
      ) : null}
    </>
  );
};
