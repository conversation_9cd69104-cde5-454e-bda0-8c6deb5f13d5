// Mock AsyncStorage
import mockAsyncStorage from '@react-native-community/async-storage/jest/async-storage-mock';

jest.mock('@react-native-community/async-storage', () => mockAsyncStorage);

global.fetch = jest.fn();
global.console = {
  ...console,
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
};

jest.mock('react-router-dom',()=>({
  ...jest.requireActual('react-router-dom'),
  useHistory: () => ({
    listen: () => jest.fn(),
    goBack: jest.fn(),
  }),
  useLocation: ()=>({
    pathname: '/',
    search: '',
    hash: '',
    state: null,
    key: 'default',
  }),
}))

jest
  .setTimeout(30000)
  .mock('./src/shared/animated-wrappers/FadeView')
  .mock('./src/api/getPermissionApi')
  .mock('./src/api/getBindingInfoApi')
  .mock('./src/api/getBindingContractApi')
  .mock('./src/api/requestOTPApi')
  .mock('./src/api/getSubmissionStatusApi')
  .mock('./src/api/getSubmissionStatusV2Api')
  .mock('./src/api/postBasicInfoApi')
  .mock('./src/api/submitRepayOrderApi')
  .mock('./src/api/postResetBinding')
  .mock('./src/api/getListSourceOfFunds')
  .mock('./src/api/getLatestSignatureApi')
  .mock('./src/api/getAccountLockInfoApi')
  .mock('./src/api/getSummaryStatementApi')
  .mock('./src/api/getAutoRepaymentBindingStatus')
  .mock('./src/api/postAutoRepaymentRegister')
  .mock('./src/api/postAutoRepaymentUnregister')
  .mock('./src/api/getAccountApprovalInfoApi')
  .mock('./src/api/getAdResourceProductPageApi')
  .mock('./src/api/getAdResourcePopupApi')
  .mock('./src/api/postUtilitiesPrioritySOFApi')
  .mock('./src/api/getUtilitiesStatusApi')
  .mock('./src/api/getRoutingInfoApi')
  .mock('./src/api/renew_overdraft/getRenewOverDraftStatusApi')
  .mock('./src/api/renew_overdraft/postVerifyRenewFaceChallengeApi')
  .mock('./src/api/renew_overdraft/postInitRenewSignContractApi')
  .mock('./src/api/renew_overdraft/postCreateRenewRequestApi')
  .mock('./src/api/renew_overdraft/postConfirmRenewRequestApi')
  .mock('./src/api/renew_overdraft/getRenewContractApi')
  .mock('./src/api/renew_overdraft/postRequestRenewOTPApi')
  .mock('./src/api/renew_overdraft/postVerifyRenewOTPApi')
  .mock('./src/api/renew_overdraft/postUpdateRenewProfileApi')
  .mock('./src/api/update_nfc')
  .mock('./src/api/postDetailInfoApi')
  .mock('./src/shared/ZaloPayModules/ZaloPayModules')
  .mock('./src/shared/react-native-customized/AppModal')
  .mock('./src/shared/hook/useFocusEffect')
  .mock('./src/api/getTransactionHistoryApi')
  .mock('./src/api/getTransHisByAccountIdApi')
  .mock('./src/api/postCheckKycWhitelist')
  .mock('./src/api/getABTestingResultApi')
  .mock('./src/api/fetchResourcesApi')
  .mock('./src/api/postVerifyProfile')
  .mock('./src/api/getAssetInventory')
  .mock('./src/shared/react-native-customized/FSAppState')
  .mock('./src/shared/utils/versionUtils/getNativeVersion')
  .mock('./src/shared/ImagePlayer/ImagePlayer')
  .mock('./src/api/postResetBindingDueChangePhone');

jest.mock('react-native-webview', () => ({
  __esModule: true,
  default: require('./src/shared/jest/WebViewMock').WebView,
  WebView: require('./src/shared/jest/WebViewMock').WebView,
}));

jest.mock('@sentry/react-native', () => ({ init: () => jest.fn() }));

jest.mock('react-native/Libraries/LayoutAnimation/LayoutAnimation', () => ({
  ...require.requireActual('react-native/Libraries/LayoutAnimation/LayoutAnimation'),
  configureNext: jest.fn(),
}));

jest.mock('react-native-device-info', () => ({
  getDeviceId: jest.fn(),
  getUniqueId: jest.fn(),
  getVersion: jest.fn(),
  getModel: jest.fn(),
}));

jest.mock('bnpl-shared/src/utils/tracking/trackEvent', () => ({
  trackEvent: jest.fn(),
  setDefaultTrackingParams: jest.fn(),
}));

jest.mock('bnpl-shared/src/shared/sentry', () => ({ captureMessage: jest.fn() }));

jest.mock('@zpi/looknfeel-icons', () => ({
  __esModule: true,
  default: 'MockedIcon',
  // Add any specific icon components that your code uses
  GeneralBackIc24: 'MockedIconComponent',
  GeneralInfoLineSecondary: 'MockedIconComponent',
  GeneralQuestionLineSecondary: 'MockedIconComponent',
  GeneralTransactionSecondary: 'MockedIconComponent',
  GeneralBankcardSecondary: 'MockedIconComponent',
  GeneralSupportIc24: 'MockedIconComponent',
  GeneralInfoIc24: 'MockedIconComponent',
  GeneralQuestionIc24: 'MockedIconComponent',
  ServiceFinanceInstallmentSecondary: "MockedIconComponent"
}));

jest.mock('bnpl-shared/src/shared/utils', () => ({
  ...require.requireActual('bnpl-shared/src/shared/utils'),
  avoidCacheImageUrl: url => url,
}));

jest.mock('bnpl-shared/src/utils', () => ({
  ...require.requireActual('bnpl-shared/src/utils'),
  getCurrentDate: jest.fn().mockReturnValue({
    getTime: jest.fn(),
  }),
  checkNativeFeatureAvailable: jest.fn(),
  getSafeView: jest.fn().mockReturnValue(20),
  getBottomSafe: jest.fn().mockReturnValue(20),
  calcBottomSafeSpacer: jest.fn().mockReturnValue(20),
  stringToBase64: value => `base64_${value}`,
  getZpiOS: jest.fn(),
}));

jest.mock('bnpl-shared/src/shared/ZaloPayModules', () => ({
  ...jest.requireActual('bnpl-shared/src/shared/ZaloPayModules'),
  usePromptAuthChallengeFlow: () => ({
    promptAuthChallengeFlow: jest.fn(),
  }),

}));

jest.mock('bnpl-shared/src/features/location_picker', () => {
  const React = require('react');
  const { TouchableOpacity, Text } = require('react-native');

  const mockLocationData = {
    permissionStatus: 'PERMISSION_ALWAYS',
    shouldOpenSetting: false,
    location: {
      id: 1,
      name: 'Ho Chi Minh City',
      city_name: 'Ho Chi Minh City',
      latitude: 10.8231,
      longitude: 106.6297
    }
  };

  const MockLocationPicker = ({ title, onLocationChange, onLayout, children, ...props }) => {
    return React.createElement(TouchableOpacity, {
      testID: 'mock-location-picker',
      onPress: () => onLocationChange?.(mockLocationData),
      onLayout,
      ...props
    }, React.createElement(Text, {}, title || 'Mock Location Picker'));
  };

  return {
    __esModule: true,
    LocationPicker: MockLocationPicker,
    default: MockLocationPicker,
    PermissionStatus: {
      PERMISSION_DENIED: 'PERMISSION_DENIED',
      PERMISSION_NOT_GRANTED: 'PERMISSION_NOT_GRANTED',
      PERMISSION_ALWAYS: 'PERMISSION_ALWAYS',
      PERMISSION_WHEN_USE: 'PERMISSION_WHEN_USE',
    },
    CurrentLocation: {},
    CityModel: {}
  };
});


jest.mock('@zpi/looknfeel-icons', () => ({
  __esModule: true,
  default: 'MockedIcon',
  // Add any specific icon components that your code uses
  GeneralBackIc24: 'MockedIconComponent',
  GeneralInfoLineSecondary: 'MockedIconComponent',
  GeneralQuestionLineSecondary: 'MockedIconComponent',
  GeneralTransactionSecondary: 'MockedIconComponent',
  GeneralBankcardSecondary: 'MockedIconComponent',
  GeneralSupportIc24: 'MockedIconComponent',
  GeneralInfoIc24: 'MockedIconComponent',
  GeneralQuestionIc24: 'MockedIconComponent',
  ServiceFinanceInstallmentSecondary: "MockedIconComponent"
}));